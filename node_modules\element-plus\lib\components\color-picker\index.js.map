{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/color-picker/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport ColorPicker from './src/color-picker.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElColorPicker: SFCWithInstall<typeof ColorPicker> =\n  withInstall(ColorPicker)\nexport default ElColorPicker\n\nexport * from './src/color-picker'\n"], "names": ["withInstall", "ColorPicker"], "mappings": ";;;;;;;;AAEY,MAAC,aAAa,GAAGA,mBAAW,CAACC,wBAAW;;;;;;;;"}